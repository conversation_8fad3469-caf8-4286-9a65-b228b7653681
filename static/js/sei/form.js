/**
 * SEI Form - Alpine.js Form Logic
 * Refactored for improved code quality and maintainability
 */

/**
 * Constants for form configuration
 */
const FORM_CONSTANTS = {
    // URL configuration - centralized URL path management
    URL_PREFIX: '/sei',
    API_BASE_PATH: '/api',

    // Validation limits
    MIN_NAME_LENGTH: 3,
    MAX_NAME_LENGTH: 150,
    MIN_SERVICE_TYPE_LENGTH: 3,
    MAX_SERVICE_TYPE_LENGTH: 255,
    MIN_REVISION: 1,
    MAX_REVISION: 99,
    MAX_STEPS: 10, // 0-10 = 11 steps total

    // Session storage keys
    SESSION_STEP_KEY: 'seiFormStep',
    SESSION_DATA_KEY: 'seiFormData',

    // Regex patterns
    PATTERNS: {
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        numeric: /^\d+$/,
        uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    }
};

// Dynamically build API endpoints using URL constants
FORM_CONSTANTS.API_ENDPOINTS = {
    unidade: `${FORM_CONSTANTS.URL_PREFIX}${FORM_CONSTANTS.API_BASE_PATH}/unidade/`,
    interessado: `${FORM_CONSTANTS.URL_PREFIX}${FORM_CONSTANTS.API_BASE_PATH}/interessado/`,
    localizacao: `${FORM_CONSTANTS.URL_PREFIX}${FORM_CONSTANTS.API_BASE_PATH}/localizacao/`,
    assunto: `${FORM_CONSTANTS.URL_PREFIX}${FORM_CONSTANTS.API_BASE_PATH}/assunto/`,
    local: `${FORM_CONSTANTS.URL_PREFIX}${FORM_CONSTANTS.API_BASE_PATH}/local/`,
    disciplina: `${FORM_CONSTANTS.URL_PREFIX}${FORM_CONSTANTS.API_BASE_PATH}/disciplina/`,
    submit: `${FORM_CONSTANTS.URL_PREFIX}${FORM_CONSTANTS.API_BASE_PATH}/submit/`
};

// URL builder utility functions
FORM_CONSTANTS.URLS = {
    /**
     * Build success page URL
     * @param {string} protocoloId - Protocol ID
     * @returns {string} Success page URL
     */
    buildSuccessUrl: (protocoloId) => `${FORM_CONSTANTS.URL_PREFIX}/success/${encodeURIComponent(protocoloId)}/`,

    /**
     * Check if current path is within the SEI application
     * @returns {boolean} True if current path starts with URL_PREFIX
     */
    isWithinApp: () => window.location.pathname.startsWith(FORM_CONSTANTS.URL_PREFIX),

    /**
     * Check if current path is the success page
     * @returns {boolean} True if current path includes '/success/'
     */
    isSuccessPage: () => window.location.pathname.includes('/success/')
};

/**
 * Step configuration with validation rules and behaviors
 */
const STEP_CONFIG = [
    {
        index: 0,
        title: 'Requisitante',
        key: 'requisitante',
        validator: 'validateRequisitanteStep',
        isValid: 'isRequisitanteStepValid'
    },
    {
        index: 1,
        title: 'Unidade',
        key: 'unidade',
        validator: 'validateSelectStep',
        isValid: 'isSelectStepValid',
        hasNotApplicable: true
    },
    {
        index: 2,
        title: 'Interessado',
        key: 'interessado',
        validator: 'validateSelectStep',
        isValid: 'isSelectStepValid',
        hasNotApplicable: true
    },
    {
        index: 3,
        title: 'Localização',
        key: 'localizacao',
        validator: 'validateSelectStep',
        isValid: 'isSelectStepValid',
        hasNotApplicable: true
    },
    {
        index: 4,
        title: 'Assunto',
        key: 'assunto',
        validator: 'validateSelectStep',
        isValid: 'isSelectStepValid',
        hasNotApplicable: false
    },
    {
        index: 5,
        title: 'Serviço',
        key: 'servico',
        validator: 'validateServicoStep',
        isValid: 'isServicoStepValid'
    },
    {
        index: 6,
        title: 'Local',
        key: 'local',
        validator: 'validateSelectStep',
        isValid: 'isSelectStepValid',
        hasNotApplicable: true
    },
    {
        index: 7,
        title: 'Disciplina',
        key: 'disciplina',
        validator: 'validateSelectStep',
        isValid: 'isSelectStepValid',
        hasNotApplicable: true
    },
    {
        index: 8,
        title: 'Revisão',
        key: 'revisao',
        validator: 'validateRevisaoStep',
        isValid: 'isRevisaoStepValid'
    },
    {
        index: 9,
        title: 'Nro. SEI',
        key: 'seiNumber',
        validator: 'validateSeiNumberStep',
        isValid: 'isSeiNumberStepValid'
    },
    {
        index: 10,
        title: 'Análise',
        key: 'review',
        validator: null,
        isValid: 'isReviewStepValid'
    }
];

/**
 * Select2 field configurations
 */
const SELECT2_CONFIG = {
    unidade: {
        placeholder: 'Selecione uma unidade ou digite para buscar',
        endpoint: 'unidade',
        hasNotApplicable: true
    },
    interessado: {
        placeholder: 'Selecione um interessado ou digite para buscar',
        endpoint: 'interessado',
        hasNotApplicable: true
    },
    localizacao: {
        placeholder: 'Selecione uma localização ou digite para buscar',
        endpoint: 'localizacao',
        hasNotApplicable: true
    },
    assunto: {
        placeholder: 'Selecione um assunto ou digite para buscar',
        endpoint: 'assunto',
        hasNotApplicable: false
    },
    local: {
        placeholder: 'Selecione um local ou digite para buscar',
        endpoint: 'local',
        hasNotApplicable: true
    },
    disciplina: {
        placeholder: 'Selecione uma disciplina ou digite para buscar',
        endpoint: 'disciplina',
        hasNotApplicable: true
    }
};

/**
 * Main SEI Form function
 * @returns {Object} Alpine.js component object
 */
function seiForm() {
    return {
        // State properties
        currentStep: 0,
        isLoading: false,
        showSuccessModal: false,
        errors: [],

        // Configuration (backward compatibility)
        steps: STEP_CONFIG.map(step => ({ title: step.title, key: step.key })),

        // Form data structure
        formData: {
            requisitante: {
                nome: '',
                email: '',
                email_confirm: ''
            },
            unidade: {
                id: '',
                text: '',
                notApplicable: false
            },
            interessado: {
                id: '',
                text: '',
                notApplicable: false
            },
            localizacao: {
                id: '',
                text: '',
                notApplicable: false
            },
            assunto: {
                id: '',
                text: ''
            },
            servico: {
                codigo: '',
                tipo_servico: ''
            },
            local: {
                id: '',
                text: '',
                notApplicable: false
            },
            disciplina: {
                id: '',
                text: '',
                notApplicable: false
            },
            revisao: {
                value: '',
                formatted: ''
            },
            seiNumber: {
                numero: ''
            }
        },

        /**
         * Initialize the form component
         */
        init() {
            this.loadFromSession();
            this.$nextTick(() => {
                this.initializeSelect2();
            });
        },

        // ========================================
        // VALIDATION METHODS
        // ========================================

        /**
         * Validate a specific step using configuration-driven approach
         * @param {number} stepIndex - The step index to validate
         * @returns {boolean} True if step is valid
         */
        validateStep(stepIndex) {
            this.clearErrors();

            const stepConfig = STEP_CONFIG[stepIndex];
            if (!stepConfig || !stepConfig.validator) {
                return true;
            }

            // Ensure the validator method exists before calling it
            if (typeof this[stepConfig.validator] !== 'function') {
                console.warn(`Validator method ${stepConfig.validator} not found for step ${stepIndex}`);
                return true;
            }

            // Call the appropriate validator method
            return this[stepConfig.validator](stepIndex, stepConfig);
        },

        /**
         * Check if a step is valid without clearing errors
         * @param {number} stepIndex - The step index to check
         * @returns {boolean} True if step is valid
         */
        isStepValid(stepIndex) {
            const stepConfig = STEP_CONFIG[stepIndex];
            if (!stepConfig || !stepConfig.isValid) {
                return true;
            }

            // Ensure the method exists before calling it
            if (typeof this[stepConfig.isValid] !== 'function') {
                console.warn(`Validation method ${stepConfig.isValid} not found for step ${stepIndex}`);
                return true;
            }

            // Call the appropriate validation check method
            return this[stepConfig.isValid](stepIndex, stepConfig);
        },

        /**
         * Validate requisitante step (step 0)
         * @param {number} _stepIndex - Step index (unused)
         * @param {Object} _stepConfig - Step configuration (unused)
         * @returns {boolean} True if valid
         */
        validateRequisitanteStep(_stepIndex, _stepConfig) {
            const { nome, email, email_confirm } = this.formData.requisitante;

            // Validate nome
            const nomeValue = nome.trim();
            if (!nomeValue) {
                this.addError('Nome é obrigatório');
            } else if (nomeValue.length < FORM_CONSTANTS.MIN_NAME_LENGTH) {
                this.addError('Nome deve ter pelo menos 3 caracteres');
            } else if (nomeValue.length > FORM_CONSTANTS.MAX_NAME_LENGTH) {
                this.addError('Nome deve ter no máximo 150 caracteres');
            }

            // Validate email fields
            if (!email.trim()) {
                this.addError('E-mail é obrigatório');
            }
            if (!email_confirm.trim()) {
                this.addError('Confirmação de e-mail é obrigatória');
            }

            // Validate email format and match
            this.validateEmail();

            return this.errors.length === 0;
        },

        /**
         * Check if requisitante step is valid
         * @returns {boolean} True if valid
         */
        isRequisitanteStepValid() {
            const { nome, email, email_confirm } = this.formData.requisitante;
            const nomeValue = nome.trim();
            const emailValid = email.trim() && email_confirm.trim() && email === email_confirm;
            const nomeValid = nomeValue &&
                             nomeValue.length >= FORM_CONSTANTS.MIN_NAME_LENGTH &&
                             nomeValue.length <= FORM_CONSTANTS.MAX_NAME_LENGTH;

            return nomeValid && emailValid;
        },

        /**
         * Validate select-based steps (unidade, interessado, etc.)
         * @param {number} _stepIndex - Step index (unused)
         * @param {Object} stepConfig - Step configuration
         * @returns {boolean} True if valid
         */
        validateSelectStep(_stepIndex, stepConfig) {
            const fieldData = this.formData[stepConfig.key];

            if (!fieldData.id && (!stepConfig.hasNotApplicable || !fieldData.notApplicable)) {
                this.addError(`Seleção de ${stepConfig.title.toLowerCase()} é obrigatória`);
            }

            return this.errors.length === 0;
        },

        /**
         * Check if select-based step is valid
         * @param {number} _stepIndex - Step index (unused)
         * @param {Object} stepConfig - Step configuration
         * @returns {boolean} True if valid
         */
        isSelectStepValid(_stepIndex, stepConfig) {
            const fieldData = this.formData[stepConfig.key];
            return !!fieldData.id || (stepConfig.hasNotApplicable && fieldData.notApplicable);
        },

        /**
         * Validate serviço step (step 5)
         * @param {number} _stepIndex - Step index (unused)
         * @param {Object} _stepConfig - Step configuration (unused)
         * @returns {boolean} True if valid
         */
        validateServicoStep(_stepIndex, _stepConfig) {
            const { codigo, tipo_servico } = this.formData.servico;

            // Validate codigo if provided (optional field)
            if (codigo && !FORM_CONSTANTS.PATTERNS.numeric.test(codigo)) {
                this.addError('Código do serviço deve conter apenas números');
            }

            // Validate tipo_servico if provided (optional field)
            const servicoTipoValue = tipo_servico.trim();
            if (servicoTipoValue) {
                if (servicoTipoValue.length < FORM_CONSTANTS.MIN_SERVICE_TYPE_LENGTH) {
                    this.addError('Tipo de serviço deve ter pelo menos 3 caracteres');
                } else if (servicoTipoValue.length > FORM_CONSTANTS.MAX_SERVICE_TYPE_LENGTH) {
                    this.addError('Tipo de serviço deve ter no máximo 255 caracteres');
                }
            }

            return this.errors.length === 0;
        },

        /**
         * Check if serviço step is valid
         * @returns {boolean} True if valid
         */
        isServicoStepValid() {
            const { codigo, tipo_servico } = this.formData.servico;
            const codigoValid = !codigo || FORM_CONSTANTS.PATTERNS.numeric.test(codigo);
            const servicoTipoValue = tipo_servico.trim();
            const tipoValid = !servicoTipoValue ||
                             (servicoTipoValue.length >= FORM_CONSTANTS.MIN_SERVICE_TYPE_LENGTH &&
                              servicoTipoValue.length <= FORM_CONSTANTS.MAX_SERVICE_TYPE_LENGTH);

            return codigoValid && tipoValid;
        },

        /**
         * Validate revisão step (step 8)
         * @param {number} _stepIndex - Step index (unused)
         * @param {Object} _stepConfig - Step configuration (unused)
         * @returns {boolean} True if valid
         */
        validateRevisaoStep(_stepIndex, _stepConfig) {
            const { value } = this.formData.revisao;

            if (value) {
                const revisaoNum = parseInt(value);
                if (isNaN(revisaoNum) ||
                    revisaoNum < FORM_CONSTANTS.MIN_REVISION ||
                    revisaoNum > FORM_CONSTANTS.MAX_REVISION) {
                    this.addError('Revisão deve ser um número entre 1 e 99');
                }
            }
            // No error if field is empty - it's optional

            return this.errors.length === 0;
        },

        /**
         * Check if revisão step is valid
         * @returns {boolean} True if valid
         */
        isRevisaoStepValid() {
            const { value } = this.formData.revisao;
            if (!value) return true; // Empty is valid

            const revisaoNum = parseInt(value);
            return !isNaN(revisaoNum) &&
                   revisaoNum >= FORM_CONSTANTS.MIN_REVISION &&
                   revisaoNum <= FORM_CONSTANTS.MAX_REVISION;
        },

        /**
         * Validate SEI number step (step 9)
         * @param {number} _stepIndex - Step index (unused)
         * @param {Object} _stepConfig - Step configuration (unused)
         * @returns {boolean} True if valid
         */
        validateSeiNumberStep(_stepIndex, _stepConfig) {
            const { numero } = this.formData.seiNumber;

            if (numero.trim() && !FORM_CONSTANTS.PATTERNS.numeric.test(numero)) {
                this.addError('Número SEI deve conter apenas números');
            }
            // No error if field is empty - it's optional

            return this.errors.length === 0;
        },

        /**
         * Check if SEI number step is valid
         * @returns {boolean} True if valid
         */
        isSeiNumberStepValid() {
            const { numero } = this.formData.seiNumber;
            if (!numero.trim()) return true; // Empty is valid

            return FORM_CONSTANTS.PATTERNS.numeric.test(numero);
        },

        /**
         * Check if review step is valid (always valid - final step)
         * @returns {boolean} Always true
         */
        isReviewStepValid() {
            return true;
        },

        // ========================================
        // ERROR HANDLING UTILITIES
        // ========================================

        /**
         * Clear all validation errors
         */
        clearErrors() {
            this.errors = [];
        },

        /**
         * Add a validation error
         * @param {string} message - Error message to add
         */
        addError(message) {
            this.errors.push(message);
        },

        /**
         * Remove specific error messages matching a pattern
         * @param {string|RegExp} pattern - Pattern to match for removal
         */
        removeErrors(pattern) {
            if (typeof pattern === 'string') {
                this.errors = this.errors.filter(error => !error.includes(pattern));
            } else if (pattern instanceof RegExp) {
                this.errors = this.errors.filter(error => !pattern.test(error));
            }
        },

        // ========================================
        // NAVIGATION METHODS
        // ========================================

        /**
         * Navigate to the next step
         */
        nextStep() {
            if (this.validateStep(this.currentStep)) {
                if (this.currentStep < FORM_CONSTANTS.MAX_STEPS) {
                    this.currentStep++;
                    this.saveToSession();
                    this.scrollToTop();
                }
            }
        },

        /**
         * Navigate to the previous step
         */
        previousStep() {
            if (this.currentStep > 0) {
                this.currentStep--;
                this.clearErrors();
                this.saveToSession();
                this.scrollToTop();
            }
        },

        /**
         * Navigate to a specific step with validation
         * @param {number} stepIndex - Target step index
         * @returns {boolean} True if navigation was successful
         */
        navigateToStep(stepIndex) {
            // Prevent navigation to future steps (beyond current + 1)
            if (stepIndex > this.currentStep) {
                return false;
            }

            // Allow navigation to completed steps or current step
            if (stepIndex <= this.currentStep) {
                this.currentStep = stepIndex;
                this.saveToSession();
                this.scrollToTop();
                return true;
            }

            // Allow navigation to next step only if current step is valid
            if (stepIndex === this.currentStep + 1 && this.isStepValid(this.currentStep)) {
                if (this.validateStep(this.currentStep)) {
                    this.currentStep = stepIndex;
                    this.saveToSession();
                    this.scrollToTop();
                    return true;
                }
            }

            return false;
        },

        /**
         * Scroll to top of page smoothly
         */
        scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        },

        // ========================================
        // SESSION MANAGEMENT
        // ========================================

        /**
         * Save current state to session storage
         */
        saveToSession() {
            sessionStorage.setItem(FORM_CONSTANTS.SESSION_STEP_KEY, this.currentStep);
            sessionStorage.setItem(FORM_CONSTANTS.SESSION_DATA_KEY, JSON.stringify(this.formData));
        },

        /**
         * Load state from session storage
         */
        loadFromSession() {
            const savedStep = sessionStorage.getItem(FORM_CONSTANTS.SESSION_STEP_KEY);
            const savedData = sessionStorage.getItem(FORM_CONSTANTS.SESSION_DATA_KEY);

            if (savedStep !== null) {
                this.currentStep = parseInt(savedStep);
            }

            if (savedData) {
                try {
                    const parsedData = JSON.parse(savedData);
                    this.formData = { ...this.formData, ...parsedData };

                    this.$nextTick(() => {
                        this.restoreSelect2Values();
                    });
                } catch (e) {
                    console.error('Error parsing saved form data:', e);
                }
            }
        },

        /**
         * Restore Select2 values from saved data
         */
        restoreSelect2Values() {
            const fieldsToRestore = ['unidade', 'interessado', 'localizacao', 'assunto', 'local', 'disciplina'];

            fieldsToRestore.forEach(fieldName => {
                const fieldData = this.formData[fieldName];
                const config = SELECT2_CONFIG[fieldName];

                // Only restore if we have data and it's not marked as "Not Applicable"
                if (fieldData.id && fieldData.text &&
                    (!config.hasNotApplicable || !fieldData.notApplicable)) {
                    const option = new Option(fieldData.text, fieldData.id, true, true);
                    $(`#${fieldName}_select`).append(option).trigger('change');
                }
            });
        },

        /**
         * Clear session storage
         */
        clearSession() {
            sessionStorage.removeItem(FORM_CONSTANTS.SESSION_STEP_KEY);
            sessionStorage.removeItem(FORM_CONSTANTS.SESSION_DATA_KEY);
        },

        // ========================================
        // SELECT2 INITIALIZATION
        // ========================================

        /**
         * Initialize all Select2 dropdowns
         */
        initializeSelect2() {
            const fieldsToInitialize = Object.keys(SELECT2_CONFIG);

            fieldsToInitialize.forEach(fieldName => {
                this.initializeSelect2Field(fieldName, SELECT2_CONFIG[fieldName]);
            });
        },

        /**
         * Initialize a single Select2 field
         * @param {string} fieldName - Name of the field
         * @param {Object} config - Field configuration
         */
        initializeSelect2Field(fieldName, config) {
            const selector = `#${fieldName}_select`;
            const self = this;

            $(selector).select2({
                width: '100%',
                placeholder: config.placeholder,
                allowClear: true,
                ajax: {
                    url: FORM_CONSTANTS.API_ENDPOINTS[config.endpoint],
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return { q: params.term || '' };
                    },
                    processResults: function (data) {
                        return { results: data.results };
                    },
                    cache: true
                }
            }).on('select2:select', function (e) {
                self.handleSelect2Selection(fieldName, e.params.data, config);
            }).on('select2:clear', function () {
                self.handleSelect2Clear(fieldName, config);
            });
        },

        /**
         * Handle Select2 selection event
         * @param {string} fieldName - Name of the field
         * @param {Object} data - Selected data
         * @param {Object} config - Field configuration
         */
        handleSelect2Selection(fieldName, data, config) {
            this.formData[fieldName].id = data.id;
            this.formData[fieldName].text = data.text;

            if (config.hasNotApplicable) {
                this.formData[fieldName].notApplicable = false;
            }

            this.saveToSession();
        },

        /**
         * Handle Select2 clear event
         * @param {string} fieldName - Name of the field
         * @param {Object} config - Field configuration
         */
        handleSelect2Clear(fieldName, config) {
            this.formData[fieldName].id = '';
            this.formData[fieldName].text = '';

            if (config.hasNotApplicable) {
                this.formData[fieldName].notApplicable = false;
            }

            this.saveToSession();
        },

        /**
         * Handle "Not Applicable" checkbox changes
         * @param {string} fieldName - Name of the field
         * @param {boolean} isChecked - Whether checkbox is checked
         */
        handleNotApplicableChange(fieldName, isChecked) {
            if (isChecked) {
                // Clear the Select2 field and disable it
                $(`#${fieldName}_select`).val(null).trigger('change');
                this.formData[fieldName].id = '';
                this.formData[fieldName].text = '';
                this.formData[fieldName].notApplicable = true;
            } else {
                // Re-enable the field
                this.formData[fieldName].notApplicable = false;
            }

            this.saveToSession();
        },

        // ========================================
        // FORM SUBMISSION
        // ========================================

        /**
         * Submit the form data
         */
        async submitForm() {
            if (!this.validateStep(this.currentStep)) {
                return;
            }

            this.isLoading = true;

            try {
                // Prepare submission data (trim text fields before submission)
                const submissionData = {
                    requisitante: {
                        nome: this.formData.requisitante.nome.trim(),
                        email: this.formData.requisitante.email.trim()
                    },
                    unidade_id: this.formData.unidade.notApplicable ? 'NA' : (this.formData.unidade.id || null),
                    interessado_id: this.formData.interessado.notApplicable ? 'NA' : (this.formData.interessado.id || null),
                    localizacao_id: this.formData.localizacao.notApplicable ? 'NA' : (this.formData.localizacao.id || null),
                    assunto_id: this.formData.assunto.id || null,
                    servico_codigo: this.formData.servico.codigo || '',
                    servico_tipo: this.formData.servico.tipo_servico.trim(),
                    local_id: this.formData.local.notApplicable ? 'NA' : (this.formData.local.id || null),
                    disciplina_id: this.formData.disciplina.notApplicable ? 'NA' : (this.formData.disciplina.id || null),
                    doc_revisao: this.formData.revisao.formatted,
                    doc_sei_num: this.formData.seiNumber.numero
                };

                const response = await fetch(FORM_CONSTANTS.API_ENDPOINTS.submit, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    body: JSON.stringify(submissionData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    await this.handleSubmissionSuccess(result);
                } else {
                    throw new Error(result.message || 'Erro ao enviar formulário');
                }
            } catch (error) {
                console.error('Error submitting form:', error);
                this.errors = [error.message || 'Erro ao enviar formulário. Tente novamente.'];
            } finally {
                this.isLoading = false;
            }
        },

        /**
         * Handle successful form submission
         * @param {Object} result - Submission result
         */
        async handleSubmissionSuccess(result) {
            // Validate that we have the required protocolo_id
            if (!result.protocolo_id) {
                throw new Error('Protocolo criado com sucesso, mas ID não foi retornado. Recarregue a página.');
            }

            // Validate protocolo_id format (should be a UUID)
            if (!FORM_CONSTANTS.PATTERNS.uuid.test(result.protocolo_id)) {
                throw new Error('ID do protocolo inválido. Recarregue a página.');
            }

            // Clear form data from session before redirecting
            this.clearSession();

            try {
                await this.redirectToSuccess(result);
            } catch (redirectError) {
                console.error('Redirection failed:', redirectError);
                this.showManualRedirectOption(result);
            }
        },

        /**
         * Redirect to success page
         * @param {Object} result - Submission result
         */
        async redirectToSuccess(result) {
            const successUrl = FORM_CONSTANTS.URLS.buildSuccessUrl(result.protocolo_id);
            console.log('Redirecting to:', successUrl);

            // Show redirection message
            this.isLoading = true;
            this.errors = ['Protocolo criado com sucesso! Redirecionando...'];

            // Perform the redirection
            window.location.href = successUrl;

            // Fallback: if redirection doesn't happen within 5 seconds, show manual link
            setTimeout(() => {
                if (FORM_CONSTANTS.URLS.isWithinApp() && !FORM_CONSTANTS.URLS.isSuccessPage()) {
                    this.isLoading = false;
                    const userConfirm = confirm(
                        `Protocolo criado com sucesso!\n` +
                        `Código: ${result.doc_cod}\n\n` +
                        `A página não foi redirecionada automaticamente.\n` +
                        `Clique em OK para ir para a página de confirmação.`
                    );
                    if (userConfirm) {
                        window.location.href = successUrl;
                    }
                }
            }, 5000);
        },

        /**
         * Show manual redirect option when automatic redirect fails
         * @param {Object} result - Submission result
         */
        showManualRedirectOption(result) {
            const userConfirm = confirm(
                `Protocolo criado com sucesso!\n` +
                `Código: ${result.doc_cod}\n\n` +
                `Clique em OK para ver os detalhes do protocolo.`
            );
            if (userConfirm) {
                window.open(FORM_CONSTANTS.URLS.buildSuccessUrl(result.protocolo_id), '_blank');
            }
        },

        // ========================================
        // UTILITY METHODS
        // ========================================

        /**
         * Close success modal and reset form
         */
        closeSuccessModal() {
            this.showSuccessModal = false;
            this.resetForm();
        },

        /**
         * Reset form to initial state
         */
        resetForm() {
            this.currentStep = 0;
            this.formData = {
                requisitante: { nome: '', email: '', email_confirm: '' },
                unidade: { id: '', text: '', notApplicable: false },
                interessado: { id: '', text: '', notApplicable: false },
                localizacao: { id: '', text: '', notApplicable: false },
                assunto: { id: '', text: '' },
                servico: { codigo: '', tipo_servico: '' },
                local: { id: '', text: '', notApplicable: false },
                disciplina: { id: '', text: '', notApplicable: false },
                revisao: { value: '', formatted: '' },
                seiNumber: { numero: '' }
            };

            // Clear Select2 selections
            const fieldsToReset = ['unidade', 'interessado', 'localizacao', 'assunto', 'local', 'disciplina'];
            fieldsToReset.forEach(fieldName => {
                $(`#${fieldName}_select`).val(null).trigger('change');
            });
        },

        /**
         * Get CSRF token from Django cookies
         * @returns {string} CSRF token
         */
        getCSRFToken() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'csrftoken') {
                    return value;
                }
            }
            return '';
        },



        // ========================================
        // INPUT FORMATTING AND VALIDATION
        // ========================================

        /**
         * Prevent non-numeric characters from being typed
         * @param {Event} event - Input event
         */
        validateNumericInput(event) {
            const char = String.fromCharCode(event.which);
            if (!/[0-9]/.test(char)) {
                event.preventDefault();
            }
        },

        /**
         * Validate and format serviço código input (numeric only)
         * @param {Event} event - Input event
         */
        validateServicoCodigoInput(event) {
            const value = event.target.value;
            const numericValue = value.replace(/[^0-9]/g, '');

            if (value !== numericValue) {
                this.formData.servico.codigo = numericValue;
                event.target.value = numericValue;
            }

            this.removeErrors('Código do serviço deve conter apenas números');
        },

        /**
         * Format revisão value as R + zero-padded 2-digit number
         */
        formatRevisao() {
            const value = parseInt(this.formData.revisao.value);
            if (!isNaN(value) &&
                value >= FORM_CONSTANTS.MIN_REVISION &&
                value <= FORM_CONSTANTS.MAX_REVISION) {
                this.formData.revisao.formatted = 'R' + value.toString().padStart(2, '0');
            } else {
                this.formData.revisao.formatted = '';
            }

            this.removeErrors('Revisão');
        },

        /**
         * Validate revisão input (numeric only)
         * @param {Event} event - Input event
         */
        validateRevisaoInput(event) {
            const char = String.fromCharCode(event.which);
            if (!/[0-9]/.test(char)) {
                event.preventDefault();
            }
        },

        /**
         * Validate and format SEI number input (numeric only)
         * @param {Event} event - Input event
         */
        validateSeiNumberInput(event) {
            const value = event.target.value;
            const numericValue = value.replace(/[^0-9]/g, '');

            if (value !== numericValue) {
                this.formData.seiNumber.numero = numericValue;
                event.target.value = numericValue;
            }

            this.removeErrors('Número SEI');
        },

        /**
         * Format nome input (uppercase, preserve spaces during typing)
         * @param {Event} event - Input event
         */
        formatNomeInput(event) {
            const value = event.target.value.toUpperCase();
            this.formData.requisitante.nome = value;

            this.removeErrors(/Nome|nome/);

            // Real-time validation using trimmed value
            const trimmedValue = value.trim();
            if (trimmedValue) {
                if (trimmedValue.length < FORM_CONSTANTS.MIN_NAME_LENGTH) {
                    this.addError('Nome deve ter pelo menos 3 caracteres');
                } else if (trimmedValue.length > FORM_CONSTANTS.MAX_NAME_LENGTH) {
                    this.addError('Nome deve ter no máximo 150 caracteres');
                }
            } else if (value.length > 0) {
                this.addError('Nome é obrigatório');
            }

            this.saveToSession();
        },

        /**
         * Format email input (lowercase, trim whitespace)
         * @param {Event} event - Input event
         */
        formatEmailInput(event) {
            const value = event.target.value.trim().toLowerCase();
            this.formData.requisitante.email = value;
            event.target.value = value;
            this.validateEmail();
            this.saveToSession();
        },

        /**
         * Format email confirmation input (lowercase, trim whitespace)
         * @param {Event} event - Input event
         */
        formatEmailConfirmInput(event) {
            const value = event.target.value.trim().toLowerCase();
            this.formData.requisitante.email_confirm = value;
            event.target.value = value;
            this.validateEmail();
            this.saveToSession();
        },

        /**
         * Format serviço tipo input (uppercase, preserve spaces during typing)
         * @param {Event} event - Input event
         */
        formatServicoTipoInput(event) {
            const value = event.target.value.toUpperCase();
            this.formData.servico.tipo_servico = value;

            this.removeErrors(/Tipo de serviço|tipo de serviço/);

            // Real-time validation using trimmed value (optional field)
            const trimmedValue = value.trim();
            if (trimmedValue) {
                if (trimmedValue.length < FORM_CONSTANTS.MIN_SERVICE_TYPE_LENGTH) {
                    this.addError('Tipo de serviço deve ter pelo menos 3 caracteres');
                } else if (trimmedValue.length > FORM_CONSTANTS.MAX_SERVICE_TYPE_LENGTH) {
                    this.addError('Tipo de serviço deve ter no máximo 255 caracteres');
                }
            }

            this.saveToSession();
        },

        /**
         * Validate email format and confirmation match
         */
        validateEmail() {
            this.removeErrors(/E-mail|email/);

            const { email, email_confirm } = this.formData.requisitante;

            // Validate email format
            if (email && !FORM_CONSTANTS.PATTERNS.email.test(email)) {
                this.addError('E-mail inválido');
            }

            // Validate email confirmation match
            if (email && email_confirm && email !== email_confirm) {
                this.addError('E-mails não coincidem');
            }
        },

        // ========================================
        // DEBUG UTILITIES
        // ========================================

        /**
         * Debug function to test redirection (can be called from browser console)
         * @param {string} protocoloId - Protocol ID to test with
         */
        testRedirection(protocoloId) {
            if (!protocoloId) {
                console.error('Please provide a protocolo ID');
                return;
            }

            const successUrl = FORM_CONSTANTS.URLS.buildSuccessUrl(protocoloId);
            console.log('Testing redirection to:', successUrl);
            window.location.href = successUrl;
        }
    };
}