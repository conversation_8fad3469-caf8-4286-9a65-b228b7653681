{% load static %}
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Protocolo {{ protocolo.doc_cod }} - Sistema GERPRO</title>
    <style>
        /* PDF-optimized styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header - Table-based layout for xhtml2pdf compatibility */
        .header {
            width: 100%;
            border-bottom: 2px solid #333333;
            padding-bottom: 20px;
            margin-bottom: 30px;
            background-color: white;
            background: white;
        }

        .header-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .header-table td {
            vertical-align: middle;
            padding: 0;
            border: none;
        }

        .header-left {
            width: 200px;
            text-align: left;
            background-color: white;
            background: white;
        }

        .header-left img {
            height: 40px;
            width: auto;
            max-width: 140px;
            background-color: transparent;
            background: none;
            display: block;
        }

        .header-right {
            text-align: right;
            padding-left: 20px;
        }

        .header-right h1 {
            color: #000000;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            margin-top: 0;
        }

        .header-right .subtitle {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
        
        /* Document code highlight */
        .doc-code-section {
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
        }

        .doc-code-section h2 {
            font-size: 16px;
            color: #333;
            margin-bottom: 10px;
        }

        .doc-code {
            font-size: 18px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            color: #034EA2;
            letter-spacing: 1px;
        }
        
        /* Information sections */
        .info-section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }

        .info-section h3 {
            background: #333333;
            color: white;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        /* Use table layout for better pisa compatibility */
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .info-table td {
            width: 50%;
            padding: 0;
            vertical-align: top;
        }

        .info-table td:first-child {
            padding-right: 10px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            width: 100%;
            box-sizing: border-box;
        }

        .info-item .label {
            font-weight: bold;
            color: #555;
            font-size: 11px;
            text-transform: uppercase;
            margin-bottom: 3px;
        }

        .info-item .value {
            color: #333;
            font-size: 12px;
            word-wrap: break-word;
        }

        .info-item .value.empty {
            color: #999;
            font-style: italic;
        }

        /* Single column for wide content */
        .info-full {
            width: 100%;
            margin-right: 0;
        }
        
        /* Footer */
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #666;
            font-size: 10px;
        }
        
        .generation-info {
            margin-top: 10px;
            font-size: 9px;
            color: #999;
        }
        
        /* Page break utilities */
        .page-break {
            page-break-before: always;
        }
        
        .no-break {
            page-break-inside: avoid;
        }
        
        /* Print-specific styles */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .container {
                max-width: none;
                margin: 0;
                padding: 15px;
            }
        }
        
        @page {
            margin: 2cm;
            size: A4;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <table class="header-table">
                <tr>
                    <td class="header-left">
                        <img src="{% static 'images/sei/logo-artesp-white-opt.png' %}" alt="ARTESP Logo">
                    </td>
                    <td class="header-right">
                        <h1>Sistema GERPRO</h1>
                        <div class="subtitle">Protocolo de Solicitação</div>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Document Code Section -->
        <div class="doc-code-section no-break">
            <h2>Código do Documento</h2>
            <div class="doc-code">{{ protocolo.doc_cod }}</div>
        </div>

        <!-- Requisitante Information -->
        <div class="info-section no-break">
            <h3>1. Dados do Requisitante</h3>
            <table class="info-table">
                <tr>
                    <td>
                        <div class="info-item">
                            <div class="label">Nome Completo</div>
                            <div class="value">{{ protocolo.usuario.nome|default:"Não informado" }}</div>
                        </div>
                    </td>
                    <td>
                        <div class="info-item">
                            <div class="label">E-mail</div>
                            <div class="value">{{ protocolo.usuario.email|default:"Não informado" }}</div>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Unidade Information -->
        <div class="info-section no-break">
            <h3>2. Unidade</h3>
            <table class="info-table">
                <tr>
                    <td>
                        <div class="info-item">
                            <div class="label">Unidade</div>
                            <div class="value{% if not protocolo.unidade.unidade %} empty{% endif %}">
                                {{ protocolo.unidade.unidade|default:"Não informado" }}
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="info-item">
                            <div class="label">Código</div>
                            <div class="value{% if not protocolo.unidade.codigo %} empty{% endif %}">
                                {{ protocolo.unidade.codigo|default:"Não informado" }}
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Interessado Information -->
        <div class="info-section no-break">
            <h3>3. Interessado</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="label">Código</div>
                    <div class="value{% if not protocolo.interessado.codigo %} empty{% endif %}">
                        {{ protocolo.interessado.codigo|default:"Não informado" }}
                    </div>
                </div>
                <div class="info-item">
                    <div class="label">Concessão</div>
                    <div class="value{% if not protocolo.interessado.concessao %} empty{% endif %}">
                        {{ protocolo.interessado.concessao|default:"Não informado" }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Localização Information -->
        <div class="info-section no-break">
            <h3>4. Localização</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="label">Localização</div>
                    <div class="value{% if not protocolo.localizacao.localizacao %} empty{% endif %}">
                        {{ protocolo.localizacao.localizacao|default:"Não informado" }}
                    </div>
                </div>
                <div class="info-item">
                    <div class="label">Código</div>
                    <div class="value{% if not protocolo.localizacao.codigo %} empty{% endif %}">
                        {{ protocolo.localizacao.codigo|default:"Não informado" }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Assunto Information -->
        <div class="info-section no-break">
            <h3>5. Assunto</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="label">Descrição</div>
                    <div class="value{% if not protocolo.assunto.descricao %} empty{% endif %}">
                        {{ protocolo.assunto.descricao|default:"Não informado" }}
                    </div>
                </div>
                <div class="info-item">
                    <div class="label">Código</div>
                    <div class="value{% if not protocolo.assunto.codigo %} empty{% endif %}">
                        {{ protocolo.assunto.codigo|default:"Não informado" }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Serviço Information -->
        <div class="info-section no-break">
            <h3>6. Serviço</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="label">Código do Serviço</div>
                    <div class="value{% if not protocolo.servico_codigo %} empty{% endif %}">
                        {{ protocolo.servico_codigo|default:"NÃO INFORMADO OU NÃO APLICÁVEL" }}
                    </div>
                </div>
                <div class="info-item">
                    <div class="label">Tipo de Serviço</div>
                    <div class="value{% if not protocolo.servico_tipo %} empty{% endif %}">
                        {{ protocolo.servico_tipo|default:"NÃO INFORMADO OU NÃO APLICÁVEL" }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Local Information -->
        <div class="info-section no-break">
            <h3>7. Local</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="label">Local</div>
                    <div class="value{% if not protocolo.local.local %} empty{% endif %}">
                        {{ protocolo.local.local|default:"Não informado" }}
                    </div>
                </div>
                <div class="info-item">
                    <div class="label">Código</div>
                    <div class="value{% if not protocolo.local.codigo %} empty{% endif %}">
                        {{ protocolo.local.codigo|default:"Não informado" }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Disciplina Information -->
        <div class="info-section no-break">
            <h3>8. Disciplina</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="label">Disciplina</div>
                    <div class="value{% if not protocolo.disciplina.disciplina %} empty{% endif %}">
                        {{ protocolo.disciplina.disciplina|default:"Não informado" }}
                    </div>
                </div>
                <div class="info-item">
                    <div class="label">Código</div>
                    <div class="value{% if not protocolo.disciplina.codigo %} empty{% endif %}">
                        {{ protocolo.disciplina.codigo|default:"Não informado" }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="info-section no-break">
            <h3>9. Informações Adicionais</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="label">Revisão</div>
                    <div class="value{% if not protocolo.doc_revisao %} empty{% endif %}">
                        {{ protocolo.doc_revisao|default:"NÃO INFORMADO OU NÃO APLICÁVEL" }}
                    </div>
                </div>
                <div class="info-item">
                    <div class="label">Número SEI</div>
                    <div class="value{% if not protocolo.doc_sei_num %} empty{% endif %}">
                        {{ protocolo.doc_sei_num|default:"NÃO INFORMADO OU NÃO APLICÁVEL" }}
                    </div>
                </div>
                <div class="info-item">
                    <div class="label">Data de Criação</div>
                    <div class="value">{{ protocolo.created_at|date:"d/m/Y H:i" }}</div>
                </div>
                <div class="info-item">
                    <div class="label">Última Atualização</div>
                    <div class="value">{{ protocolo.updated_at|date:"d/m/Y H:i" }}</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div>© 2025 ARTESP - Sistema GERPRO - Protocolo de Solicitação</div>
            <div class="generation-info">
                Documento gerado em {% now "d/m/Y H:i:s" %} | ID: {{ protocolo.id }}
            </div>
        </div>
    </div>
</body>
</html>
