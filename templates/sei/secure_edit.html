<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edição Segura do Protocolo - Sistema SEI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#FF161F',
                        'primary-light': '#ffeeee',
                        'secondary': '#034EA2',
                        'accent': '#0B9247',
                        'accent-dark': '#047857',
                        'highlight': '#FBB900',
                        'gray-darkest': '#000000',
                        'gray-darker': '#333333',
                        'gray-dark': '#4b5563',
                        'gray-medium': '#6b7280',
                        'gray-light': '#9ca3af',
                        'gray-lighter': '#d1d5db',
                        'gray-lightest': '#f3f4f6'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-gray-darkest shadow-lg border-b border-gray-dark">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-white">ARTESP - Sistema GERPRO</h1>
                    <span class="ml-4 text-gray-light">Edição Segura do Protocolo</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-light text-sm">
                        Protocolo: <strong>{{ protocolo.doc_cod }}</strong>
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Security Notice -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.414-4.414a2 2 0 00-2.828 0L9 10.172 7.414 8.586a2 2 0 00-2.828 2.828l3 3a2 2 0 002.828 0l9-9a2 2 0 000-2.828z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-xl font-bold text-green-800">Acesso Autorizado</h2>
                    <p class="text-green-700 mt-2">
                        Você está autenticado para editar o protocolo <strong>{{ protocolo.doc_cod }}</strong>.
                        Apenas o campo "Número SEI" pode ser editado.
                    </p>
                </div>
            </div>
        </div>

        <!-- Display Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="mb-4 p-4 rounded-md {% if message.tags == 'error' %}bg-red-50 border border-red-200 text-red-700{% elif message.tags == 'success' %}bg-green-50 border border-green-200 text-green-700{% else %}bg-blue-50 border border-blue-200 text-blue-700{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <!-- Protocol Information (Read-Only) -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span class="bg-gray-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">📋</span>
                Informações do Protocolo (Somente Leitura)
            </h3>

            <!-- Document Code Highlight -->
            <div class="bg-primary-light bg-opacity-10 border border-primary-light rounded-lg p-4 mb-6">
                <div class="text-center">
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Código do Documento</h4>
                    <div class="text-3xl font-mono font-bold text-primary">{{ protocolo.doc_cod }}</div>
                </div>
            </div>

            <!-- Protocol Details Grid (Read-Only) -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Requisitante Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Requisitante</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Nome:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.usuario.nome }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">E-mail:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.usuario.email }}</span>
                        </div>
                    </div>
                </div>

                <!-- Unidade Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Unidade</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Unidade:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.unidade.unidade|default:"Não informado" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.unidade.codigo|default:"Não informado" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Serviço Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Serviço</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.servico_codigo|default:"NÃO INFORMADO OU NÃO APLICÁVEL" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Tipo:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.servico_tipo|default:"NÃO INFORMADO OU NÃO APLICÁVEL" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Informações Adicionais</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Revisão:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.doc_revisao|default:"NÃO INFORMADO OU NÃO APLICÁVEL" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Data de Criação:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.created_at|date:"d/m/Y H:i" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Editable Section -->
        <div class="bg-green-50 rounded-lg shadow-lg p-6">
            <h3 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span class="bg-accent text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">✏️</span>
                Campo Editável
            </h3>

            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Editable doc_sei_num Field -->
                <div class="bg-white rounded-lg p-6 border-2 border-accent border-dashed">
                    <label for="doc_sei_num" class="block text-lg font-medium text-gray-900 mb-2">
                        Número SEI
                    </label>
                    <div class="mb-4">
                        <span class="text-sm text-gray-600">
                            Valor atual: <span class="font-mono">{{ protocolo.doc_sei_num|default:"(vazio)" }}</span>
                        </span>
                    </div>
                    <input type="text" 
                           id="doc_sei_num" 
                           name="doc_sei_num" 
                           value="{{ protocolo.doc_sei_num|default:'' }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-accent focus:border-accent text-lg"
                           placeholder="Digite o número SEI"
                           maxlength="50">
                    <p class="mt-2 text-sm text-gray-500">
                        Este é o único campo que pode ser editado. Máximo de 50 caracteres.
                    </p>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button type="submit"
                            class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-accent hover:bg-accent-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Salvar Alterações
                    </button>

                    <a href="{% url 'sei:protocolo_access' %}"
                       class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Cancelar
                    </a>
                </div>
            </form>
        </div>

        <!-- Security Information -->
        <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Informações de Segurança</h3>
                    <div class="mt-1 text-sm text-yellow-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Esta sessão expira em 30 minutos por motivos de segurança</li>
                            <li>Após salvar as alterações, você será desconectado automaticamente</li>
                            <li>Apenas o campo "Número SEI" pode ser editado nesta interface</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-darkest text-white mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center">
                <p class="text-sm text-gray-light">
                    © 2025 ARTESP - Sistema GERPRO - Todos os direitos reservados
                </p>
            </div>
        </div>
    </footer>
</body>
</html>
