<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Protocolo Atualizado com Sucesso - Sistema SEI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#FF161F',
                        'primary-light': '#ffeeee',
                        'secondary': '#034EA2',
                        'accent': '#0B9247',
                        'accent-dark': '#047857',
                        'highlight': '#FBB900',
                        'gray-darkest': '#000000',
                        'gray-darker': '#333333',
                        'gray-dark': '#4b5563',
                        'gray-medium': '#6b7280',
                        'gray-light': '#9ca3af',
                        'gray-lighter': '#d1d5db',
                        'gray-lightest': '#f3f4f6'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-gray-darkest shadow-lg border-b border-gray-dark">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-white">ARTESP - Sistema GERPRO</h1>
                    <span class="ml-4 text-gray-light">Protocolo Atualizado com Sucesso</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'sei:sei_form' %}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Novo Protocolo
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Success Message -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-xl font-bold text-green-800">Protocolo Atualizado com Sucesso!</h2>
                    <p class="text-green-700 mt-2">
                        As alterações no protocolo <strong>{{ protocolo.doc_cod }}</strong> foram salvas com sucesso.
                        Por motivos de segurança, sua sessão foi encerrada automaticamente.
                    </p>
                </div>
            </div>
        </div>

        <!-- Display Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="mb-4 p-4 rounded-md {% if message.tags == 'error' %}bg-red-50 border border-red-200 text-red-700{% elif message.tags == 'success' %}bg-green-50 border border-green-200 text-green-700{% else %}bg-blue-50 border border-blue-200 text-blue-700{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <!-- Protocol Information -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">📋</span>
                Informações Atualizadas do Protocolo
            </h3>

            <!-- Document Code Highlight -->
            <div class="bg-primary-light bg-opacity-10 border border-primary-light rounded-lg p-4 mb-6">
                <div class="text-center">
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Código do Documento</h4>
                    <div class="text-3xl font-mono font-bold text-primary">{{ protocolo.doc_cod }}</div>
                </div>
            </div>

            <!-- Protocol Details Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Requisitante Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Requisitante</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Nome:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.usuario.nome }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">E-mail:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.usuario.email }}</span>
                        </div>
                    </div>
                </div>

                <!-- Unidade Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Unidade</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Unidade:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.unidade.unidade|default:"Não informado" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.unidade.codigo|default:"Não informado" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Serviço Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Serviço</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.servico_codigo|default:"NÃO INFORMADO OU NÃO APLICÁVEL" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Tipo:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.servico_tipo|default:"NÃO INFORMADO OU NÃO APLICÁVEL" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Updated Information -->
                <div class="bg-green-50 rounded-lg p-4 border-2 border-green-200">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Campo Atualizado
                    </h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Número SEI:</span>
                            <span class="ml-2 text-gray-900 font-mono bg-white px-2 py-1 rounded border">
                                {{ protocolo.doc_sei_num|default:"(vazio)" }}
                            </span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Última Atualização:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.updated_at|date:"d/m/Y H:i" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'sei:protocolo_access' %}"
               class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-secondary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                </svg>
                Acessar Outro Protocolo
            </a>

            <a href="{% url 'sei:sei_form' %}"
               class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Criar Novo Protocolo
            </a>

            <button onclick="window.print()"
                    class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Imprimir Protocolo
            </button>
        </div>

        <!-- Security Notice -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Informações Importantes</h3>
                    <div class="mt-1 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Sua sessão de edição foi encerrada automaticamente por motivos de segurança</li>
                            <li>Para fazer novas alterações, será necessário acessar novamente com sua senha</li>
                            <li>As alterações foram salvas permanentemente no sistema</li>
                            <li>Um novo e-mail de confirmação pode ser enviado se configurado</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-darkest text-white mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center">
                <p class="text-sm text-gray-light">
                    © 2025 ARTESP - Sistema GERPRO - Todos os direitos reservados
                </p>
            </div>
        </div>
    </footer>

    <!-- Print Styles -->
    <style>
        @media print {
            body { background: white !important; }
            header, footer, .no-print { display: none !important; }
            .bg-gray-50, .bg-gray-100 { background: white !important; }
            .shadow-lg { box-shadow: none !important; }
        }
    </style>
</body>
</html>
