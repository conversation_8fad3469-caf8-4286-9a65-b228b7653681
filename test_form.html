<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEI Form Test</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .step { margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
        .error { color: red; margin: 10px 0; }
        .button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:disabled { background: #ccc; cursor: not-allowed; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div x-data="seiForm()" x-init="init()">
        <h1>SEI Form Test</h1>
        
        <!-- Error Display -->
        <div x-show="errors.length > 0" class="error">
            <ul>
                <template x-for="error in errors" :key="error">
                    <li x-text="error"></li>
                </template>
            </ul>
        </div>

        <!-- Step 0: Requisitante -->
        <div x-show="currentStep === 0" class="step">
            <h2>Step 0: Requisitante</h2>
            <input type="text" placeholder="Nome" x-model="formData.requisitante.nome" @input="formatNomeInput($event)">
            <input type="email" placeholder="Email" x-model="formData.requisitante.email" @input="formatEmailInput($event)">
            <input type="email" placeholder="Confirm Email" x-model="formData.requisitante.email_confirm" @input="formatEmailConfirmInput($event)">
        </div>

        <!-- Step 1: Unidade -->
        <div x-show="currentStep === 1" class="step">
            <h2>Step 1: Unidade</h2>
            <select id="unidade_select" style="width: 300px;"></select>
            <label>
                <input type="checkbox" x-model="formData.unidade.notApplicable" @change="handleNotApplicableChange('unidade', $event.target.checked)">
                Not Applicable
            </label>
        </div>

        <!-- Navigation -->
        <div>
            <button class="button" @click="previousStep()" x-show="currentStep > 0">← Previous</button>
            <button class="button" 
                    :disabled="!isStepValid(currentStep)" 
                    @click="nextStep()" 
                    x-show="currentStep < 10">
                Next →
            </button>
        </div>

        <!-- Debug Info -->
        <div style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 5px;">
            <h3>Debug Info</h3>
            <p>Current Step: <span x-text="currentStep"></span></p>
            <p>Step Valid: <span x-text="isStepValid(currentStep)"></span></p>
            <p>Errors: <span x-text="errors.length"></span></p>
        </div>
    </div>

    <script src="static/js/sei/form.js"></script>
</body>
</html>
